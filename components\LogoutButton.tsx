'use client';

import React, { useState } from 'react';
import { logoutClient } from '../lib/auth';

interface LogoutButtonProps {
  className?: string;
  showText?: boolean;
}

const LogoutButton: React.FC<LogoutButtonProps> = ({ 
  className = '', 
  showText = true 
}) => {
  const [isLoggingOut, setIsLoggingOut] = useState(false);

  const handleLogout = async () => {
    if (isLoggingOut) return;
    
    setIsLoggingOut(true);
    
    try {
      // إظهار تأكيد قبل تسجيل الخروج
      const confirmed = window.confirm('هل أنت متأكد من تسجيل الخروج؟');
      
      if (confirmed) {
        console.log('🚪 Logging out...');
        logoutClient();
      } else {
        setIsLoggingOut(false);
      }
    } catch (error) {
      console.error('Logout error:', error);
      setIsLoggingOut(false);
      // في حالة الخطأ، محاولة تسجيل الخروج المباشر
      logoutClient();
    }
  };

  return (
    <button
      onClick={handleLogout}
      disabled={isLoggingOut}
      className={`
        inline-flex items-center gap-2 px-4 py-2 
        bg-red-500 hover:bg-red-600 
        text-white font-medium rounded-lg 
        transition-all duration-200 
        disabled:opacity-50 disabled:cursor-not-allowed
        transform hover:scale-105 active:scale-95
        ${className}
      `}
      title="تسجيل الخروج"
    >
      {isLoggingOut ? (
        <>
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
          {showText && <span>جاري الخروج...</span>}
        </>
      ) : (
        <>
          <i className="ri-logout-box-line text-lg"></i>
          {showText && <span>تسجيل الخروج</span>}
        </>
      )}
    </button>
  );
};

export default LogoutButton;
