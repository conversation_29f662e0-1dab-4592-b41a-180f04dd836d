'use client';

import React, { useState } from 'react';
import { Locale } from '../lib/i18n';
import { useSiteSettings } from '../hooks/useSiteSettings';

interface PartnersSectionProps {
  locale: Locale;
}

const PartnersSection: React.FC<PartnersSectionProps> = ({ locale }) => {
  const { settings, loading } = useSiteSettings();
  const [hoveredPartner, setHoveredPartner] = useState<number | null>(null);

  // القيم الافتراضية في حالة عدم وجود إعدادات
  const defaultPartners = [
    {
      nameEn: 'Hilton Hotels',
      nameAr: 'فنادق هيلتون',
      logo: 'https://logos-world.net/wp-content/uploads/2020/06/Hilton-Logo.png',
      description: 'Trusted partner in hospitality',
      descriptionAr: 'شريك موثوق في قطاع الفندقة'
    },
    {
      nameEn: 'Mar<PERSON><PERSON>',
      nameAr: 'ماريوت',
      logo: 'https://logos-world.net/wp-content/uploads/2020/06/Marriott-Logo.png',
      description: 'Distinguished strategic cooperation',
      descriptionAr: 'تعاون استراتيجي مميز'
    },
    {
      nameEn: 'Four Seasons',
      nameAr: 'فور سيزونز',
      logo: 'https://logos-world.net/wp-content/uploads/2020/06/Four-Seasons-Logo.png',
      description: 'Partnership in excellence and quality',
      descriptionAr: 'شراكة في التميز والجودة'
    },
    {
      nameEn: 'Hyatt',
      nameAr: 'حياة',
      logo: 'https://logos-world.net/wp-content/uploads/2020/06/Hyatt-Logo.png',
      description: 'Long-term relationship',
      descriptionAr: 'علاقة طويلة الأمد'
    }
  ];

  const defaultContent = {
    ar: {
      title: 'شركاؤنا',
      subtitle: 'نفخر بثقة كبرى الشركات والفنادق العالمية بنا',
      description: 'نعمل مع أفضل الشركات في قطاع الضيافة لتوفير أعلى معايير الجودة والخدمة',
      trustText: 'يثق بنا أكثر من',
      clientsCount: '500+',
      clientsText: 'عميل حول العالم'
    },
    en: {
      title: 'Our Partners',
      subtitle: 'We are proud of the trust of major international companies and hotels',
      description: 'We work with the best companies in the hospitality sector to provide the highest standards of quality and service',
      trustText: 'Trusted by more than',
      clientsCount: '500+',
      clientsText: 'clients worldwide'
    }
  };

  // استخدام الإعدادات من لوحة التحكم أو القيم الافتراضية
  const getPartnersContent = () => {
    if (!settings?.partnersSettings) {
      return {
        ...defaultContent[locale],
        partners: defaultPartners.map(partner => ({
          name: locale === 'ar' ? partner.nameAr : partner.nameEn,
          logo: partner.logo,
          description: locale === 'ar' ? partner.descriptionAr : partner.description
        }))
      };
    }

    return {
      title: locale === 'ar'
        ? (settings.partnersSettings.titleAr || defaultContent[locale].title)
        : (settings.partnersSettings.title || defaultContent[locale].title),
      subtitle: locale === 'ar'
        ? (settings.partnersSettings.descriptionAr || defaultContent[locale].subtitle)
        : (settings.partnersSettings.description || defaultContent[locale].subtitle),
      description: defaultContent[locale].description,
      trustText: defaultContent[locale].trustText,
      clientsCount: defaultContent[locale].clientsCount,
      clientsText: defaultContent[locale].clientsText,
      partners: settings.partnersSettings.items.length > 0
        ? settings.partnersSettings.items.map(partner => ({
            name: locale === 'ar' ? partner.nameAr : partner.nameEn,
            logo: partner.logo,
            description: locale === 'ar' ? partner.descriptionAr : partner.description,
            website: partner.website
          }))
        : defaultPartners.map(partner => ({
            name: locale === 'ar' ? partner.nameAr : partner.nameEn,
            logo: partner.logo,
            description: locale === 'ar' ? partner.descriptionAr : partner.description
          }))
    };
  };

  const currentContent = getPartnersContent();

  // عرض حالة التحميل
  if (loading) {
    return (
      <section className="py-20 bg-gradient-to-br from-gray-50 to-white">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <i className="ri-loader-4-line text-4xl text-primary animate-spin mb-4"></i>
            <p className="text-gray-600">
              {locale === 'ar' ? 'جاري التحميل...' : 'Loading...'}
            </p>
          </div>
        </div>
      </section>
    );
  }

  // إخفاء القسم إذا كان معطلاً
  if (settings?.partnersSettings && !settings.partnersSettings.enabled) {
    return null;
  }

  return (
    <section className="relative py-24 bg-gradient-to-br from-slate-50 via-white to-blue-50/30 overflow-hidden">
      {/* خلفية متحركة */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-10 w-72 h-72 bg-gradient-to-br from-primary/10 to-blue-500/10 rounded-full animate-blob opacity-60"></div>
        <div className="absolute top-40 right-20 w-64 h-64 bg-gradient-to-br from-purple-500/10 to-pink-500/10 rounded-full animate-blob animation-delay-2000 opacity-60"></div>
        <div className="absolute bottom-20 left-1/4 w-56 h-56 bg-gradient-to-br from-green-500/10 to-teal-500/10 rounded-full animate-blob animation-delay-4000 opacity-60"></div>

        {/* خطوط ديكورية */}
        <div className="absolute top-0 left-1/3 w-px h-full bg-gradient-to-b from-transparent via-primary/10 to-transparent"></div>
        <div className="absolute top-0 right-1/3 w-px h-full bg-gradient-to-b from-transparent via-purple-500/10 to-transparent"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        {/* Header محسن */}
        <div className="text-center mb-20">
          {/* شارة القسم */}
          <div className="inline-flex items-center gap-3 bg-white/90 backdrop-blur-sm border-2 border-primary/20 rounded-full px-6 py-3 mb-8 shadow-xl">
            <div className="w-8 h-8 bg-gradient-to-br from-primary to-purple-600 rounded-full flex items-center justify-center">
              <i className="ri-team-line text-white text-sm"></i>
            </div>
            <span className="text-primary text-sm font-bold uppercase tracking-wider">
              {locale === 'ar' ? 'شركاؤنا المتميزون' : 'Our Distinguished Partners'}
            </span>
          </div>

          {/* العنوان الرئيسي */}
          <h2 className="text-5xl md:text-6xl lg:text-7xl font-black mb-8 leading-tight">
            <span className="text-gray-900">
              {locale === 'ar' ? 'شركاء ' : 'Trusted '}
            </span>
            <span className="text-primary">
              {locale === 'ar' ? 'النجاح' : 'Partners'}
            </span>
          </h2>

          {/* خط زخرفي */}
          <div className="flex items-center justify-center mb-8">
            <div className="h-1 w-16 bg-gradient-to-r from-transparent to-primary rounded-full"></div>
            <div className="w-4 h-4 bg-gradient-to-br from-primary to-purple-600 rounded-full mx-4 animate-pulse"></div>
            <div className="h-1 w-16 bg-gradient-to-l from-transparent to-purple-600 rounded-full"></div>
          </div>

          {/* الوصف */}
          <div className="bg-white/70 backdrop-blur-sm rounded-2xl p-6 shadow-lg max-w-4xl mx-auto mb-8">
            <p className="text-gray-900 text-xl md:text-2xl leading-relaxed font-medium mb-4">
              {currentContent.subtitle}
            </p>
            <p className="text-gray-700 text-lg leading-relaxed">
              {currentContent.description}
            </p>
          </div>
        </div>

        {/* إحصائيات محسنة */}
        <div className="text-center mb-20">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
            {[
              {
                number: currentContent.clientsCount,
                label: currentContent.clientsText,
                icon: 'ri-user-heart-line',
                color: 'from-blue-500 to-blue-600'
              },
              {
                number: '15+',
                label: locale === 'ar' ? 'سنة خبرة' : 'Years Experience',
                icon: 'ri-time-line',
                color: 'from-green-500 to-green-600'
              },
              {
                number: '50+',
                label: locale === 'ar' ? 'دولة' : 'Countries',
                icon: 'ri-global-line',
                color: 'from-purple-500 to-purple-600'
              }
            ].map((stat, index) => (
              <div
                key={index}
                className="relative bg-white rounded-3xl p-8 shadow-xl border border-gray-100 hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 group"
                style={{ animationDelay: `${index * 200}ms` }}
              >
                {/* خلفية متدرجة */}
                <div className={`absolute inset-0 bg-gradient-to-br ${stat.color} opacity-0 group-hover:opacity-5 rounded-3xl transition-opacity duration-300`}></div>

                {/* أيقونة */}
                <div className={`w-16 h-16 bg-gradient-to-br ${stat.color} rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg group-hover:shadow-xl transition-all duration-300 group-hover:scale-110`}>
                  <i className={`${stat.icon} text-2xl text-white`}></i>
                </div>

                {/* الرقم */}
                <div className="text-4xl font-black text-gray-900 mb-3 group-hover:text-primary transition-colors duration-300">
                  {stat.number}
                </div>

                {/* التسمية */}
                <div className="text-gray-600 font-semibold text-sm uppercase tracking-wider">
                  {stat.label}
                </div>

                {/* تأثير الإضاءة */}
                <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-primary to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-t-3xl"></div>
              </div>
            ))}
          </div>
        </div>

        {/* شريط الشركاء المحسن */}
        <div className="relative mb-20">
          {/* عنوان فرعي */}
          <div className="text-center mb-12">
            <h3 className="text-2xl font-bold text-gray-800 mb-4">
              {locale === 'ar' ? 'يثق بنا رواد الصناعة' : 'Trusted by Industry Leaders'}
            </h3>
            <p className="text-gray-600">
              {locale === 'ar' ? 'شركاء نفخر بالعمل معهم' : 'Partners we are proud to work with'}
            </p>
          </div>

          {/* حاوي الشريط */}
          <div className="relative overflow-hidden bg-white/50 backdrop-blur-sm rounded-3xl p-8 shadow-xl border border-gray-200">
            {/* تأثير الإضاءة العلوية */}
            <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-primary via-purple-600 to-primary"></div>

            <div
              className={`flex gap-8 items-center animate-scroll ${
                locale === 'ar' ? 'animate-scroll-rtl' : 'animate-scroll-ltr'
              }`}
              style={{
                width: `${currentContent.partners.length * 280}px`,
              }}
            >
              {/* عرض الشركاء مرتين للحصول على حلقة مستمرة */}
              {[...currentContent.partners, ...currentContent.partners].map((partner, index) => (
                <div
                  key={index}
                  className="group relative bg-white rounded-2xl p-6 shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 hover:scale-105 text-center flex-shrink-0 overflow-hidden border border-gray-100 hover:border-primary/30"
                  style={{ width: '240px', height: '180px' }}
                  onMouseEnter={() => setHoveredPartner(index)}
                  onMouseLeave={() => setHoveredPartner(null)}
                >
                  {/* خلفية متدرجة عند التمرير */}
                  <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-purple-500/5 to-primary/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-2xl"></div>

                  {/* تأثير الإضاءة الجانبية */}
                  <div className={`absolute inset-0 bg-gradient-to-r from-primary/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 ${hoveredPartner === index ? 'animate-pulse' : ''}`}></div>

                  {/* حاوي الشعار المحسن */}
                  <div className="relative z-10 mb-6 flex items-center justify-center">
                    <div className="w-20 h-20 bg-gradient-to-br from-gray-50 to-white rounded-2xl shadow-lg border-2 border-gray-100 flex items-center justify-center group-hover:shadow-xl group-hover:border-primary/30 transition-all duration-300 group-hover:scale-110">
                      <div className="w-16 h-16 flex items-center justify-center">
                        <img
                          src={partner.logo}
                          alt={partner.name}
                          className="max-w-full max-h-full object-contain filter grayscale group-hover:grayscale-0 transition-all duration-500"
                          style={{
                            width: 'auto',
                            height: 'auto',
                            maxWidth: '60px',
                            maxHeight: '60px'
                          }}
                          onError={(e) => {
                            (e.target as HTMLImageElement).src = 'https://via.placeholder.com/60x60/f3f4f6/6b7280?text=' + encodeURIComponent(partner.name.charAt(0));
                          }}
                        />
                      </div>
                    </div>
                  </div>

                  {/* محتوى البطاقة */}
                  <div className="relative z-10">
                    {/* اسم الشريك */}
                    <h3 className="font-bold text-gray-800 mb-3 text-lg group-hover:text-primary transition-colors duration-300 leading-tight">
                      {partner.name}
                    </h3>

                    {/* خط زخرفي متحرك */}
                    <div className="w-12 h-1 bg-gradient-to-r from-primary to-purple-600 mx-auto mb-3 rounded-full transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500"></div>

                    {/* الوصف */}
                    {partner.description && (
                      <p className="text-sm text-gray-600 opacity-0 group-hover:opacity-100 transition-all duration-500 line-clamp-2 transform translate-y-3 group-hover:translate-y-0 leading-relaxed">
                        {partner.description}
                      </p>
                    )}

                    {/* رابط الموقع */}
                    {(partner as any).website && (
                      <div className="mt-3 opacity-0 group-hover:opacity-100 transition-all duration-500 transform translate-y-3 group-hover:translate-y-0">
                        <a
                          href={(partner as any).website}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="inline-flex items-center gap-2 text-sm text-primary hover:text-primary/80 transition-colors duration-300 font-medium bg-primary/10 px-3 py-1 rounded-full hover:bg-primary/20"
                        >
                          <i className="ri-external-link-line text-sm"></i>
                          {locale === 'ar' ? 'زيارة الموقع' : 'Visit Website'}
                        </a>
                      </div>
                    )}
                  </div>

                  {/* تأثير الإضاءة المحسن */}
                  <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-white/30 via-primary/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none rounded-2xl"></div>

                  {/* نقاط ديكورية */}
                  <div className="absolute top-4 right-4 w-2 h-2 bg-primary/20 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <div className="absolute bottom-4 left-4 w-1 h-1 bg-purple-500/30 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* CSS للحركة والتأثيرات */}
        <style jsx>{`
          @keyframes scroll-ltr {
            0% {
              transform: translateX(0);
            }
            100% {
              transform: translateX(-50%);
            }
          }

          @keyframes scroll-rtl {
            0% {
              transform: translateX(-50%);
            }
            100% {
              transform: translateX(0);
            }
          }

          .animate-scroll-ltr {
            animation: scroll-ltr 35s linear infinite;
          }

          .animate-scroll-rtl {
            animation: scroll-rtl 35s linear infinite;
          }

          .animate-scroll:hover {
            animation-play-state: paused;
          }

          .line-clamp-2 {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
          }

          /* تأثيرات إضافية للبطاقات */
          .group:hover {
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.15);
          }

          /* تأثير النبضة للشعار */
          .group:hover .w-16 {
            animation: pulse-logo 0.6s ease-in-out;
          }

          @keyframes pulse-logo {
            0%, 100% {
              transform: scale(1);
            }
            50% {
              transform: scale(1.05);
            }
          }
        `}</style>

        {/* قسم الدعوة للعمل المحسن */}
        <div className="text-center mt-20">
          <div className="relative bg-white rounded-3xl p-12 shadow-2xl border border-gray-200 max-w-4xl mx-auto overflow-hidden">
            {/* خلفية ديكورية */}
            <div className="absolute inset-0">
              <div className="absolute top-10 right-10 w-32 h-32 bg-primary/5 rounded-full animate-pulse"></div>
              <div className="absolute bottom-10 left-10 w-24 h-24 bg-purple-500/5 rounded-full animate-pulse animation-delay-2000"></div>
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-40 h-40 bg-gray-100/50 rounded-full animate-spin-slow"></div>
            </div>

            <div className="relative z-10">
              {/* أيقونة مركزية */}
              <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-primary to-purple-600 rounded-full mb-8 shadow-xl">
                <i className="ri-handshake-line text-3xl text-white"></i>
              </div>

              {/* العنوان */}
              <h3 className="text-4xl md:text-5xl font-black mb-6 leading-tight text-gray-900">
                {locale === 'ar'
                  ? 'كن شريكنا القادم'
                  : 'Become Our Next Partner'
                }
              </h3>

              {/* الوصف */}
              <p className="text-gray-700 text-xl mb-10 max-w-3xl mx-auto leading-relaxed">
                {locale === 'ar'
                  ? 'انضم إلى شبكة شركائنا المتميزين واستفد من خبرتنا الواسعة في قطاع الضيافة. نحن نؤمن بقوة الشراكات الاستراتيجية'
                  : 'Join our distinguished partner network and benefit from our extensive experience in the hospitality sector. We believe in the power of strategic partnerships'
                }
              </p>

              {/* الأزرار */}
              <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
                <a
                  href={`/${locale}/contact`}
                  className="group bg-gradient-to-r from-primary to-purple-600 hover:from-purple-600 hover:to-primary text-white px-10 py-4 rounded-2xl font-bold text-lg transition-all duration-300 transform hover:scale-105 hover:shadow-2xl flex items-center gap-3"
                >
                  <i className="ri-handshake-line text-xl group-hover:animate-pulse"></i>
                  <span>{locale === 'ar' ? 'ابدأ الشراكة' : 'Start Partnership'}</span>
                  <i className="ri-arrow-right-line transition-transform duration-300 group-hover:translate-x-1"></i>
                </a>

                <a
                  href={`/${locale}/about`}
                  className="group border-2 border-primary text-primary hover:bg-primary hover:text-white px-10 py-4 rounded-2xl font-bold text-lg transition-all duration-300 transform hover:scale-105 flex items-center gap-3"
                >
                  <i className="ri-information-line text-xl"></i>
                  <span>{locale === 'ar' ? 'اعرف المزيد' : 'Learn More'}</span>
                </a>
              </div>

              {/* معلومات إضافية */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-12 pt-8 border-t border-gray-200">
                <div className="text-center">
                  <div className="text-2xl font-bold text-primary mb-2">
                    <i className="ri-award-line"></i>
                  </div>
                  <div className="text-gray-700 text-sm font-medium">
                    {locale === 'ar' ? 'شراكات مميزة' : 'Premium Partnerships'}
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-primary mb-2">
                    <i className="ri-global-line"></i>
                  </div>
                  <div className="text-gray-700 text-sm font-medium">
                    {locale === 'ar' ? 'انتشار عالمي' : 'Global Reach'}
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-primary mb-2">
                    <i className="ri-rocket-line"></i>
                  </div>
                  <div className="text-gray-700 text-sm font-medium">
                    {locale === 'ar' ? 'نمو مستدام' : 'Sustainable Growth'}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default PartnersSection;
